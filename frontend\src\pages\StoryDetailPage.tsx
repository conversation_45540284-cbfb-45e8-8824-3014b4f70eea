import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { ArrowRight, MessageSquare, Heart, Eye, Twitter, Linkedin, Globe, ChevronDown, Share2, Bookmark, Copy, ChevronUp, MapPin, Briefcase, BookOpen, ArrowLeft, Home, Crown, Lock } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import CommentSection from '@/components/CommentSection';
import RelatedStories from '@/components/RelatedStories';
import Layout from "@/components/Layout";
import SEO from "@/components/SEO";
import UserLogin from "@/components/UserLogin";
import CaseStudySubscriptionModal from '@/components/CaseStudySubscriptionModal';
import { Story } from '@/types';
import { getStory, toggleStoryLike } from '@/services/storyService';
import favoriteService from '@/services/favoriteService';
import caseStudySubscriptionService from '@/services/caseStudySubscriptionService';
import { formatDate, cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { useSEO } from "@/hooks/useSEO";
import { useScrollLoginPopup } from "@/hooks/useScrollLoginPopup";
import { generateStoryStructuredData } from "@/utils/seoUtils";
import { Helmet } from 'react-helmet-async';
import { useAuth } from '@/lib/AuthContext';

// API URL from environment or fallback to localhost
const API_URL = import.meta.env['VITE_API_URL'] || 'http://localhost:5000';
// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

const StoryDetailPage = () => {
  const { storyId } = useParams<{ storyId: string }>();
  const [story, setStory] = useState<Story | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [scrollToTopVisible, setScrollToTopVisible] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [isSubscriptionModalOpen, setIsSubscriptionModalOpen] = useState(false);

  const { generateSEO } = useSEO();
  const { isAuthenticated, user } = useAuth();

  // Scroll-based login popup
  const scrollLoginPopup = useScrollLoginPopup({
    threshold: 45, // Show popup at 45% scroll
    sessionKey: 'story-scroll-login-popup',
    enabled: true,
    compulsory: true // Make popup compulsory - users must login/register
  });

  // Helper function to determine if a story is liked by the current user
  const isLikedByUser = (story: Story): boolean => {
    // Don't show as liked if we don't have authenticated user data
    if (!isAuthenticated || !user?.id || !story.likedBy) {
      return false;
    }

    // Check if the current user's ID is in the likedBy array
    const isLiked = story.likedBy.some(likedUserId => {
      if (!likedUserId) return false;
      // Convert both to strings for comparison to handle ObjectId/string differences
      return likedUserId.toString() === user.id.toString();
    });

    return isLiked;
  };

  // Helper function to get complete image URL
  const getImageUrl = (path: string) => {
    if (!path) return '/images/default-story-cover.jpg';
    if (path.startsWith('http')) return path;
    if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
    return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLDivElement, Event>) => {
    const target = e.target as HTMLDivElement;
    target.style.backgroundImage = `url('/images/default-story-cover.jpg')`;
  };

  useEffect(() => {
    const fetchStory = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!storyId) {
          throw new Error('Story ID is required');
        }

        const response = await getStory(storyId);
        if (response.success) {
          setStory(response.data);
        } else {
          throw new Error(response.error || 'Failed to fetch story');
        }
      } catch (err: any) {
        // Check if this is a specific "story not published" error
        if (err?.response?.status === 403 && err?.response?.data?.code === 'STORY_NOT_PUBLISHED') {
          setError('STORY_NOT_PUBLISHED');
        } else {
          setError(err instanceof Error ? err.message : 'An error occurred');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchStory();
  }, [storyId]);

  // Check if story is bookmarked and liked when user is authenticated and story is loaded
  useEffect(() => {
    const checkBookmarkStatus = async () => {
      if (isAuthenticated && story) {
        try {
          const response = await favoriteService.isFavorited('story', story._id);
          if (response.success && response.data) {
            setIsBookmarked(response.data.isFavorited);
          }
        } catch (error) {
          console.error('Error checking bookmark status:', error);
        }
      }
    };

    const checkLikedStatus = () => {
      if (story) {
        setIsLiked(isLikedByUser(story));
      }
    };

    checkBookmarkStatus();
    checkLikedStatus();
  }, [isAuthenticated, story]);

  // Scroll progress and scroll-to-top functionality
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;

      setScrollProgress(scrollPercent);

      // Show scroll-to-top button when scrolled down 300px
      if (scrollTop > 300) {
        setScrollToTopVisible(true);
      } else {
        setScrollToTopVisible(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLike = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    if (!story) return;

    try {
      const response = await toggleStoryLike(story._id);

      if (response.success) {
        // Update the story state with new like data
        setStory(prev => {
          if (!prev) return prev;
          return {
            ...prev,
            likes: response.data.likes,
            likedBy: response.data.likedBy
          };
        });

        // Update the local like state
        setIsLiked(response.data.isLiked);

        toast({
          title: response.data.isLiked ? "Story liked" : "Story unliked",
          description: response.data.isLiked ? "Added to your liked stories" : "Removed from your liked stories",
        });
      }
    } catch (error) {
      console.error('Error toggling like:', error);
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBookmark = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    if (!story) return;

    try {
      const response = await favoriteService.toggleFavorite('story', story._id);

      if (response.success && response.data) {
        setIsBookmarked(response.data.isFavorited);
        toast({
          title: response.data.isFavorited ? "Story bookmarked" : "Bookmark removed",
          description: response.data.isFavorited ? "Added to your favorites" : "Removed from your favorites",
        });
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast({
        title: "Error",
        description: "Failed to update bookmark status. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link copied!",
        description: "Story link copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Generate SEO data when story is loaded
  const storySEO = story ? generateSEO.story({
    _id: story._id,
    title: story.title,
    excerpt: story.excerpt,
    content: story.content,
    featuredImage: story.featuredImage,
    author: {
      name: typeof story.author === 'object' ? story.author.name : 'Unknown Author',
      username: typeof story.author === 'object' ? story.author.username : 'unknown'
    },
    category: {
      name: typeof story.category === 'object' ? story.category.name : 'Uncategorized'
    },
    tags: story.tags || [],
    createdAt: story.createdAt,
    updatedAt: story.updatedAt
  }) : null;

  // Generate structured data for the story
  const structuredData = story ? generateStoryStructuredData({
    _id: story._id,
    title: story.title,
    excerpt: story.excerpt,
    content: story.content,
    featuredImage: story.featuredImage,
    author: {
      name: typeof story.author === 'object' ? story.author.name : 'Unknown Author',
      username: typeof story.author === 'object' ? story.author.username : 'unknown'
    },
    category: {
      name: typeof story.category === 'object' ? story.category.name : 'Uncategorized'
    },
    tags: story.tags || [],
    createdAt: story.createdAt,
    updatedAt: story.updatedAt
  }) : null;

  if (isLoading) {
    return (
      <Layout>
        {/* SEO Meta Tags */}
        {storySEO && <SEO {...storySEO} />}

        {/* Structured Data */}
        {structuredData && (
          <Helmet>
            <script type="application/ld+json">
              {JSON.stringify(structuredData)}
            </script>
          </Helmet>
        )}

        {/* Reading Progress Bar */}
        <div className="fixed top-0 left-0 w-full h-1 bg-muted z-50">
          <div
            className="h-full bg-brand-600 transition-all duration-150 ease-out"
            style={{ width: `${scrollProgress}%` }}
          />
        </div>

        <div className="flex items-center justify-center min-h-screen bg-background">
          <div className="animate-pulse flex flex-col space-y-6 w-full max-w-4xl p-4">
            <div className="h-14 bg-muted rounded-lg w-3/4"></div>
            <div className="h-10 bg-muted rounded-lg w-1/2"></div>
            <div className="h-96 bg-muted rounded-lg w-full"></div>
            <div className="space-y-3">
              <div className="h-4 bg-muted rounded w-full"></div>
              <div className="h-4 bg-muted rounded w-5/6"></div>
              <div className="h-4 bg-muted rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !story) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30 flex items-center justify-center px-4">
          <div className="text-center max-w-lg mx-auto">
            <div className="relative">
              {/* Decorative background elements */}
              <div className="absolute inset-0 -top-20 -bottom-20">
                <div className="absolute top-8 right-12 w-2 h-2 bg-red-300/40 dark:bg-red-600/40 rounded-full animate-pulse"></div>
                <div className="absolute top-28 left-8 w-1 h-1 bg-red-400/60 dark:bg-red-500/60 rounded-full animate-pulse delay-75"></div>
                <div className="absolute bottom-16 right-20 w-1.5 h-1.5 bg-red-200/50 dark:bg-red-700/50 rounded-full animate-pulse delay-150"></div>
              </div>

              {/* Main content card */}
              <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-xl shadow-black/5 dark:shadow-black/20">
                {/* Large 404 text */}
                <div className="mb-6">
                  <div className="text-8xl font-bold text-transparent bg-gradient-to-br from-red-500 to-red-600 dark:from-red-400 dark:to-red-500 bg-clip-text mb-2">
                    404
                  </div>
                </div>

                {/* Icon container with enhanced styling */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/20 rounded-full flex items-center justify-center shadow-lg shadow-red-200/50 dark:shadow-red-900/20 ring-1 ring-red-200/30 dark:ring-red-700/30">
                    <BookOpen className="w-10 h-10 text-red-600 dark:text-red-400" />
                  </div>
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 w-20 h-20 mx-auto mb-4 bg-red-400/20 dark:bg-red-500/20 rounded-full blur-xl"></div>
                </div>

                <h1 className="text-2xl font-bold text-foreground mb-2 tracking-tight">
                  Story Not Found
                </h1>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  The story you're looking for doesn't exist or has been removed.
                  It may have been deleted or the URL might be incorrect.
                </p>

                {/* Enhanced action buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={() => window.history.back()}
                    className="w-full bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800 text-white font-medium py-3 rounded-xl shadow-lg shadow-brand-600/25 transition-all duration-200 hover:shadow-xl hover:shadow-brand-600/30 hover:-translate-y-0.5"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Go Back
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/stories'}
                    className="w-full bg-background/50 hover:bg-muted/50 border-border/70 hover:border-border text-foreground font-medium py-3 rounded-xl transition-all duration-200 hover:shadow-md hover:-translate-y-0.5"
                  >
                    <BookOpen className="w-4 h-4 mr-2" />
                    Browse All Stories
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => window.location.href = '/'}
                    className="w-full text-muted-foreground hover:text-foreground font-medium py-3 rounded-xl transition-all duration-200 hover:bg-muted/30"
                  >
                    <Home className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Calculate reading time (rough estimate: 200 words per minute)
  const readingTime = Math.max(1, Math.ceil(story.content.split(/\s+/).length / 200));

  return (
    <Layout>
      {/* SEO Meta Tags */}
      {storySEO && <SEO {...storySEO} />}

      {/* Structured Data */}
      {structuredData && (
        <Helmet>
          <script type="application/ld+json">
            {JSON.stringify(structuredData)}
          </script>
        </Helmet>
      )}

      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-muted z-50">
        <div
          className="h-full bg-brand-600 transition-all duration-150 ease-out"
          style={{ width: `${scrollProgress}%` }}
        />
      </div>

      <main className="pb-16 pt-8 flex-1">
        {/* Content Area */}
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="flex items-center gap-2 mb-4">
              <Badge className="bg-brand-600 hover:bg-brand-700 transition-colors text-white px-3 py-1 text-xs sm:text-sm">
                {typeof story.category === 'object' ? story.category.name : 'Uncategorized'}
              </Badge>
              {story.isPremium && (
                <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-xs sm:text-sm text-white hover:from-purple-700 hover:to-pink-700">
                  Premium
                </Badge>
              )}
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-foreground mb-4 sm:mb-6 leading-tight">
              {story.title}
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground mb-4 sm:mb-6 leading-tight">
              {story.excerpt}
            </p>
            {/* Author and Metadata Section */}
            <div className="flex flex-col gap-4 mb-6 sm:mb-8">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                {/* Author Info */}
                {typeof story.author === 'object' && (
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-brand-100 flex-shrink-0">
                      <AvatarImage
                        src={getImageUrl(story.author.avatar)}
                        alt={story.author.name}
                      />
                      <AvatarFallback>{story.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 flex-1 min-w-0">
                      {/* Author Details */}
                      <div className="flex flex-col min-w-0">
                        <div className="flex items-center gap-2">
                          <a href={`/author/@${story.author.username}`} className="font-semibold text-sm sm:text-base text-foreground hover:text-brand-600 transition-colors truncate">
                            {story.author.name}
                          </a>
                          {story.author.isVerified && (
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48" className="sm:w-5 sm:h-5 flex-shrink-0">
                              <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.91,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.92,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.92-0.825c0.71-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.92-0.316l-4.706-3.66c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.41-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.41l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                            </svg>
                          )}
                        </div>
                        {story.author.roleTitle && (
                          <span className="text-muted-foreground text-xs sm:text-sm mt-0.5 truncate">{story.author.roleTitle}</span>
                        )}
                      </div>

                      {/* Story Metadata */}
                      <div className="flex flex-wrap items-center gap-2 sm:gap-3 text-muted-foreground text-xs sm:text-sm">
                        <span className="flex items-center gap-1">
                          <Eye size={12} className="sm:w-[14px] sm:h-[14px]" />
                          {story.views.toLocaleString()} views
                        </span>
                        <span className="w-1 h-1 rounded-full bg-muted-foreground hidden sm:block"></span>
                        <span>{readingTime} min read</span>
                        <span className="w-1 h-1 rounded-full bg-muted-foreground hidden sm:block"></span>
                        <span>{formatDate(story.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-start gap-2 sm:gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLike}
                  className={`flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 ${isLiked ? 'text-red-500 border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800' : ''}`}
                  title={isAuthenticated ? (isLiked ? 'Unlike this story' : 'Like this story') : 'Login to like this story'}
                >
                  <Heart className={`h-3 w-3 sm:h-4 sm:w-4 ${isLiked ? 'fill-current' : ''}`} />
                  <span>{story.likes}</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBookmark}
                  className={`flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2 ${isBookmarked ? 'text-blue-500 border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800' : ''}`}
                  title={isAuthenticated ? (isBookmarked ? 'Remove from favorites' : 'Add to favorites') : 'Login to bookmark this story'}
                >
                  <Bookmark className={`h-3 w-3 sm:h-4 sm:w-4 ${isBookmarked ? 'fill-current' : ''}`} />
                  <span>{isBookmarked ? 'Saved' : 'Save'}</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleShare}
                  className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
                  title="Share this story"
                >
                  <Share2 className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span>Share</span>
                </Button>
              </div>
            </div>
          </motion.div>

          <div className="flex flex-col lg:flex-row gap-6 lg:gap-8 mt-6 sm:mt-8">
            {/* Main content - 75% */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="lg:w-3/4"
            >

              {/* Story content */}
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="bg-card rounded-xl shadow-sm p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-border relative"
              >
                <div className="prose prose-sm sm:prose-base lg:prose-lg max-w-none dark:prose-invert">
                  <div dangerouslySetInnerHTML={{ __html: story.content }} />
                </div>

                {/* Premium content overlay for stories without subscription */}
                {story.isPremiumContent && story.subscriptionRequired && (
                  <div className="absolute inset-0 bg-gradient-to-t from-white via-white/95 to-white/30 dark:from-gray-900 dark:via-gray-900/95 dark:to-gray-900/30 flex items-end justify-center z-10 rounded-xl">
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.2, duration: 0.3 }}
                      className="text-center bg-white dark:bg-gray-900 p-4 sm:p-6 lg:p-8 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 relative z-20"
                    >
                      <div className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/50 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                        <Crown size={24} className="text-blue-600 dark:text-blue-400 sm:w-8 sm:h-8" />
                      </div>
                      <h3 className="font-bold text-lg sm:text-xl mb-2 sm:mb-3 text-gray-900 dark:text-white">Unlock Premium Stories, Case Studies & Insider Playbooks</h3>
                      <div className="space-y-3 mb-4 sm:mb-6">
                        <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed">
                          Every scroll you just made? That's another founder getting ahead while you're stuck reading introductions.
                        </p>
                        <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm leading-relaxed">
                          500+ founders went from where you are to building $500M+ in value. Their secret? They stopped learning alone and started learning from others who'd already won.
                        </p>
                        <div className="space-y-2 mt-2">
                          <p className="text-gray-800 dark:text-gray-200 text-sm font-semibold">What you're missing:</p>
                          <div className="flex justify-center">
                            <ul className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm space-y-1 list-disc list-inside text-left">
                              <li>The exact playbooks that built $500M+ in startup value</li>
                              <li>Case studies with real numbers, real strategies, real results</li>
                              <li>Battle-tested frameworks from founders who've actually exited</li>
                              <li>A community of entrepreneurs who've been exactly where you are</li>
                            </ul>
                          </div>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm font-medium mt-2">
                          For just $197/year (less than $4/week), stop learning the expensive way and start learning from those who've already won.
                        </p>
                        <p className="text-gray-600 dark:text-gray-300 text-xs sm:text-sm italic">
                          Every day you delay is another costly mistake your competitors won't make.
                        </p>
                      </div>
                      <Button
                        onClick={() => {
                          if (!isAuthenticated) {
                            setShowLoginDialog(true);
                          } else {
                            setIsSubscriptionModalOpen(true);
                          }
                        }}
                        className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white w-full font-semibold shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base"
                        size={window.innerWidth < 640 ? "sm" : "lg"}
                      >
                        {isAuthenticated ? 'Subscribe to Continue Reading' : 'Login to Subscribe'}
                      </Button>
                    </motion.div>
                  </div>
                )}
              </motion.div>

              {/* Tags */}
              {story.tags && story.tags.length > 0 && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="mt-6 sm:mt-8 flex flex-wrap gap-2"
                >
                  {story.tags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="px-3 py-1 bg-muted hover:bg-muted/80 transition-colors cursor-pointer text-xs sm:text-sm"
                    >
                      {tag}
                    </Badge>
                  ))}
                </motion.div>
              )}

              {/* Author bio */}
              {typeof story.author === 'object' && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.8 }}
                  className="mt-6 sm:mt-8 lg:mt-12 bg-card rounded-xl shadow-sm p-3 sm:p-4 lg:p-6 xl:p-8 border border-border"
                >
                  <div className="flex flex-col items-center space-y-3 sm:space-y-0 sm:flex-row sm:items-start sm:space-x-4">
                    {/* Avatar - centered on mobile, left-aligned on larger screens */}
                    <Avatar className="h-12 w-12 sm:h-16 sm:w-16 lg:h-20 lg:w-20 ring-2 sm:ring-4 ring-brand-50 flex-shrink-0">
                      <AvatarImage
                        src={getImageUrl(story.author.avatar)}
                        alt={story.author.name}
                      />
                      <AvatarFallback className="text-sm sm:text-base lg:text-lg">{story.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>

                    {/* Author info - stacked on mobile, side-by-side on larger screens */}
                    <div className="flex-1 text-center sm:text-left space-y-2 sm:space-y-3">
                      {/* Name and verification badge */}
                      <div className="flex flex-row items-center justify-center sm:justify-start gap-2">
                        <a
                          href={`/author/@${story.author.username}`}
                          className="font-bold text-base sm:text-lg lg:text-xl hover:text-brand-600 transition-colors text-foreground"
                        >
                          {story.author.name}
                        </a>
                        {story.author.isVerified && (
                          <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48" className="sm:w-5 sm:h-5 lg:w-6 lg:h-6 flex-shrink-0">
                            <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.910,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.920,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.920-0.825c0.710-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.920-0.316l-4.706-3.660c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.410-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.410l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                          </svg>
                        )}
                      </div>

                      {/* Location and role - stacked on mobile */}
                      <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-1 sm:gap-3 text-xs sm:text-sm text-muted-foreground">
                        {story.author.location && (
                          <div className="flex items-center justify-center sm:justify-start gap-1">
                            <MapPin size={12} className="text-muted-foreground/70 flex-shrink-0" />
                            <span className="truncate">{story.author.location}</span>
                          </div>
                        )}
                        {story.author.roleTitle && (
                          <div className="flex items-center justify-center sm:justify-start gap-1">
                            <Briefcase size={12} className="text-muted-foreground/70 flex-shrink-0" />
                            <span className="truncate">{story.author.roleTitle}</span>
                          </div>
                        )}
                      </div>

                      {/* Social links - centered on mobile, left-aligned on larger screens */}
                      <div className="flex items-center justify-center sm:justify-start gap-2 sm:gap-3 pt-1">
                        {story.author.websiteUrl && (
                          <a
                            href={story.author.websiteUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                            title="Website"
                          >
                            <Globe size={14} className="sm:w-4 sm:h-4" />
                          </a>
                        )}
                        {story.author.twitterUrl && (
                          <a
                            href={story.author.twitterUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                            title="Twitter"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="sm:w-4 sm:h-4">
                              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                            </svg>
                          </a>
                        )}
                        {story.author.linkedinUrl && (
                          <a
                            href={story.author.linkedinUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                            title="LinkedIn"
                          >
                            <Linkedin size={14} className="sm:w-4 sm:h-4" />
                          </a>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Bio text - separate section for better mobile layout */}
                  {story.author.bio && (
                    <div className="mt-3 sm:mt-4 lg:mt-6 pt-3 sm:pt-4 border-t border-border/50">
                      <p className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-relaxed text-center sm:text-left">
                        {story.author.bio}
                      </p>
                    </div>
                  )}
                </motion.div>
              )}

              {/* Comment section */}
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 }}
                className="mt-8 sm:mt-12"
              >
                <CommentSection storyId={story._id} />
              </motion.div>

              {/* Related Stories */}
              {typeof story.author === 'object' && (
                <RelatedStories
                  authorId={story.author._id || story.author.id}
                  authorName={story.author.name}
                  authorUsername={story.author.username}
                  currentStoryId={story._id}
                  limit={3}
                />
              )}
            </motion.div>

            {/* Sidebar - 25% */}
            <motion.div
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 1 }}
              className="lg:w-1/4 order-first lg:order-last"
            >
              <div className="sticky top-24 space-y-4 sm:space-y-6">
                {/* Quick Stats */}
                <div className="bg-card rounded-xl shadow-sm overflow-hidden border border-border">
                  <div className="p-4 sm:p-6">
                    <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground">Story Stats</h3>
                    <div className="space-y-3 sm:space-y-4">
                      <div className="flex justify-between items-center p-2 sm:p-3 bg-muted/50 rounded-lg">
                        <span className="text-muted-foreground text-sm sm:text-base">Views</span>
                        <div className="flex items-center font-medium text-foreground text-sm sm:text-base">
                          <Eye size={14} className="mr-2 text-brand-500 sm:w-4 sm:h-4" />
                          {story.views.toLocaleString()}
                        </div>
                      </div>
                      <div className="flex justify-between items-center p-2 sm:p-3 bg-muted/50 rounded-lg">
                        <span className="text-muted-foreground text-sm sm:text-base">Likes</span>
                        <div className="flex items-center font-medium text-foreground text-sm sm:text-base">
                          <Heart size={14} className="mr-2 text-brand-500 sm:w-4 sm:h-4" />
                          {story.likes.toLocaleString()}
                        </div>
                      </div>
                      <div className="flex justify-between items-center p-2 sm:p-3 bg-muted/50 rounded-lg">
                        <span className="text-muted-foreground text-sm sm:text-base">Reading time</span>
                        <div className="font-medium text-foreground text-sm sm:text-base">
                          {readingTime} min read
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Share section */}
                <div className="bg-card rounded-xl shadow-sm overflow-hidden border border-border">
                  <div className="p-4 sm:p-6">
                    <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4 text-foreground">Share this story</h3>
                    <div className="grid grid-cols-2 gap-2 sm:gap-3">
                      <Button
                        variant="outline"
                        className="w-full flex items-center justify-center gap-1 sm:gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-xs sm:text-sm p-2 sm:p-3"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="sm:w-4 sm:h-4">
                              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                            </svg>
                       X (Twitter)
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full flex items-center justify-center gap-1 sm:gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-400 transition-colors text-xs sm:text-sm p-2 sm:p-3"
                      >
                        <Linkedin size={14} className="sm:w-[18px] sm:h-[18px]" />
                        LinkedIn
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full col-span-2 flex items-center justify-center gap-1 sm:gap-2 hover:bg-muted/50 transition-colors text-xs sm:text-sm p-2 sm:p-3"
                        onClick={handleShare}
                      >
                        <Copy size={14} className="sm:w-[18px] sm:h-[18px]" />
                        Copy Link
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </main>

      {/* Scroll to top button */}
      {scrollToTopVisible && (
        <Button
          variant="outline"
          size="icon"
          className="fixed bottom-8 right-8 rounded-full shadow-md z-50 bg-background hover:bg-muted/50"
          onClick={scrollToTop}
        >
          <ChevronUp size={20} />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}

      {/* Login Dialog */}
      <UserLogin
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
      />

      {/* Scroll-triggered Login Popup */}
      <UserLogin
        isOpen={scrollLoginPopup.isPopupOpen}
        onClose={scrollLoginPopup.closePopup}
        preventScroll={true}
        blurBackground={true}
        compulsory={scrollLoginPopup.isCompulsory}
      />

      {/* Case Study Subscription Modal for premium stories */}
      <CaseStudySubscriptionModal
        isOpen={isSubscriptionModalOpen}
        onClose={() => setIsSubscriptionModalOpen(false)}
      />
    </Layout>
  );
};

export default StoryDetailPage;