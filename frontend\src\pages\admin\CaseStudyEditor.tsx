import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Plus, X, Lock, Unlock, Save, Eye, Search, Upload, User } from 'lucide-react';
import { CaseStudySection, StartupSnapshot, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, CreateCaseStudyData } from '@/types';
import caseStudyService from '@/services/caseStudyService';
import { toast } from 'sonner';

// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

// Helper function to get complete image URL for featured images
const getImageUrl = (imageData: string | { path?: string; url?: string; filename?: string }) => {
  // Handle different input types
  let imageUrl = '';

  if (!imageData) {
    return '/placeholder.svg';
  }

  if (typeof imageData === 'string') {
    imageUrl = imageData;
  } else if (typeof imageData === 'object') {
    // Prioritize url field if available, then path, then filename
    imageUrl = imageData.url || imageData.path || imageData.filename || '';
  }

  if (!imageUrl) {
    return '/placeholder.svg';
  }

  // Handle absolute URLs
  if (imageUrl.startsWith('http')) {
    return imageUrl;
  }

  // Convert Windows-style backslashes to forward slashes
  imageUrl = imageUrl.replace(/\\/g, '/');

  // Handle relative URLs
  if (imageUrl.startsWith('/')) {
    return `${BACKEND_URL.replace(/\/$/, '')}${imageUrl}`;
  } else {
    return `${BACKEND_URL.replace(/\/$/, '')}/${imageUrl}`;
  }
};

const CaseStudyEditor: React.FC = () => {
  const navigate = useNavigate();
  const { caseStudyId } = useParams();
  const isEditing = Boolean(caseStudyId);

  // Form state
  const [title, setTitle] = useState('');
  const [subheading, setSubheading] = useState('');
  const [introduction, setIntroduction] = useState('');
  const [isPremium, setIsPremium] = useState(false);
  const [status, setStatus] = useState<'draft' | 'published' | 'archived'>('draft');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [featuredImage, setFeaturedImage] = useState<File | null>(null);
  const [featuredImagePreview, setFeaturedImagePreview] = useState<string>('');

  // Author selection
  const [selectedAuthor, setSelectedAuthor] = useState<string>('');
  const [authorSearch, setAuthorSearch] = useState('');
  const [authors, setAuthors] = useState<Author[]>([]);
  const [showAuthorDropdown, setShowAuthorDropdown] = useState(false);

  // Startup Snapshot fields
  const [snapshot, setSnapshot] = useState<StartupSnapshot>({
    founded: '',
    businessType: '',
    fundingStatus: '',
    revenue: '',
    customers: '',
    website: '',
    socialLinks: { twitter: '', linkedin: '' }
  });

  // Dynamic sections
  const [sections, setSections] = useState<CaseStudySection[]>([
    { id: '1', title: 'Problem & Opportunity', content: '', isPremium: false, order: 1 },
    { id: '2', title: 'Go-To-Market Strategy', content: '', isPremium: true, order: 2 },
    { id: '3', title: 'Growth Hacks & Frameworks', content: '', isPremium: true, order: 3 }
  ]);

  // Tools Used
  const [toolsUsed, setToolsUsed] = useState<ToolCategory[]>([
    { category: 'Marketing', tools: [] },
    { category: 'Backend', tools: [] },
    { category: 'Analytics/Hosting', tools: [] }
  ]);

  const [ctaContent, setCtaContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Load case study data if editing
  useEffect(() => {
    if (isEditing && caseStudyId) {
      loadCaseStudy(caseStudyId);
    }
  }, [isEditing, caseStudyId]);

  // Initial load of authors on component mount
  useEffect(() => {
    console.log('Component mounted, loading initial authors...');
    loadAllAuthors();
  }, []);

  // Search authors
  useEffect(() => {
    if (authorSearch.length > 0) {
      console.log('Searching authors for:', authorSearch);
      searchAuthors(authorSearch);
    } else if (authorSearch.length === 0 && !selectedAuthor) {
      console.log('Author search cleared, reloading all authors...');
      loadAllAuthors();
    }
  }, [authorSearch]);

  // Handle click outside author dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.author-search-container')) {
        setShowAuthorDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadCaseStudy = async (caseStudyId: string) => {
    try {
      setLoading(true);
      const response = await caseStudyService.getCaseStudy(caseStudyId);
      const caseStudy = response.data;

      setTitle(caseStudy.title);
      setSubheading(caseStudy.subheading);
      setIntroduction(caseStudy.introduction);
      setSnapshot(caseStudy.startupSnapshot);
      setSections(caseStudy.sections.map((section, index) => ({
        ...section,
        id: section._id || section.id || index.toString()
      })));
      setToolsUsed(caseStudy.toolsUsed);
      setCtaContent(caseStudy.ctaContent || '');

      // Handle author selection - set both ID and display name
      if (typeof caseStudy.author === 'object' && caseStudy.author) {
        console.log('Loading author from object:', caseStudy.author);
        const authorId = caseStudy.author.id || caseStudy.author._id;
        setSelectedAuthor(authorId);
        setAuthorSearch(caseStudy.author.name); // Display the author's name in the search field
      } else if (typeof caseStudy.author === 'string') {
        console.log('Loading author from string ID:', caseStudy.author);
        setSelectedAuthor(caseStudy.author);
        // If we only have the ID, we'll need to fetch the author details
        // The useEffect for authorSearch will handle loading the author list
      } else {
        console.error('Invalid author data:', caseStudy.author);
      }

      setTags(caseStudy.tags);
      setStatus(caseStudy.status);
      setIsPremium(caseStudy.isPremium);

      if (caseStudy.featuredImage) {
        setFeaturedImagePreview(getImageUrl(caseStudy.featuredImage));
      }
    } catch (error) {
      console.error('Error loading case study:', error);
      toast.error('Failed to load case study');
    } finally {
      setLoading(false);
    }
  };

  const searchAuthors = async (search: string) => {
    try {
      console.log('Searching for authors with query:', search);
      const response = await caseStudyService.searchUsers(search, 10);
      console.log('Search response:', response);
      if (response.success && response.data) {
        // Verify authors have valid IDs
        response.data.forEach((author, index) => {
          const authorId = author.id || author._id;
          if (!authorId) {
            console.error(`Author ID missing for ${author.name}:`, author);
          }
        });
        setAuthors(response.data);
        console.log('Authors loaded:', response.data.length);
      } else {
        console.error('Search failed:', response);
        setAuthors([]);
      }
    } catch (error) {
      console.error('Error searching authors:', error);
      toast.error('Failed to search authors');
      setAuthors([]);
    }
  };

  const loadAllAuthors = async () => {
    try {
      console.log('Loading all authors...');
      const response = await caseStudyService.getAllUsers(20);
      console.log('Load all authors response:', response);
      if (response.success && response.data) {
        // Verify authors have valid IDs
        response.data.forEach((author, index) => {
          const authorId = author.id || author._id;
          if (!authorId) {
            console.error(`Author ID missing for ${author.name}:`, author);
          }
        });
        setAuthors(response.data);
        console.log('All authors loaded:', response.data.length);
      } else {
        console.error('Load all authors failed:', response);
        setAuthors([]);
      }
    } catch (error) {
      console.error('Error loading authors:', error);
      toast.error('Failed to load authors');
      setAuthors([]);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFeaturedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setFeaturedImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const addTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const updateSection = (id: string, field: keyof CaseStudySection, value: any) => {
    setSections(sections.map(section =>
      section.id === id ? { ...section, [field]: value } : section
    ));
  };

  const addSection = () => {
    const newSection: CaseStudySection = {
      id: Date.now().toString(),
      title: 'New Section',
      content: '',
      isPremium: false,
      order: sections.length + 1
    };
    setSections([...sections, newSection]);
  };

  const removeSection = (id: string) => {
    setSections(sections.filter(section => section.id !== id));
  };

  const addTool = (categoryIndex: number, tool: string) => {
    if (tool) {
      const newToolsUsed = [...toolsUsed];
      newToolsUsed[categoryIndex].tools.push(tool);
      setToolsUsed(newToolsUsed);
    }
  };

  const removeTool = (categoryIndex: number, toolIndex: number) => {
    const newToolsUsed = [...toolsUsed];
    newToolsUsed[categoryIndex].tools.splice(toolIndex, 1);
    setToolsUsed(newToolsUsed);
  };

  const validateForm = (): boolean => {
    if (!title.trim()) {
      toast.error('Title is required');
      return false;
    }
    if (!subheading.trim()) {
      toast.error('Subheading is required');
      return false;
    }
    if (!introduction.trim()) {
      toast.error('Introduction is required');
      return false;
    }
    if (!selectedAuthor || String(selectedAuthor).trim() === '') {
      toast.error('Please select an author');
      return false;
    }
    if (!snapshot.founded || !snapshot.businessType || !snapshot.fundingStatus ||
      !snapshot.revenue || !snapshot.customers || !snapshot.website) {
      toast.error('Please fill in all startup snapshot fields');
      return false;
    }
    return true;
  };

  const handleSave = async (saveStatus: 'draft' | 'published' | 'archived' = status) => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      // Debug logging
      console.log('Saving case study with author:', selectedAuthor);
      console.log('Author search value:', authorSearch);

      // Clean up sections - remove frontend-generated IDs for new case studies
      const cleanedSections = sections.map((section, index) => {
        const cleanSection: any = {
          title: section.title,
          content: section.content,
          isPremium: section.isPremium,
          order: index + 1
        };

        // Only include _id if it's a valid MongoDB ObjectId (24 hex characters) for updates
        if (isEditing && section.id && section.id.length === 24 && /^[0-9a-fA-F]{24}$/.test(section.id)) {
          cleanSection._id = section.id;
        }

        return cleanSection;
      });

      const caseStudyData: CreateCaseStudyData = {
        title,
        subheading,
        introduction,
        startupSnapshot: snapshot,
        sections: cleanedSections,
        toolsUsed,
        ctaContent,
        author: selectedAuthor,
        tags,
        status: saveStatus,
        isPremium,
        publishDate: saveStatus === 'published' ? new Date().toISOString() : undefined
      };

      console.log('Case study data being sent:', caseStudyData);

      if (isEditing && caseStudyId) {
        await caseStudyService.updateCaseStudy(caseStudyId, caseStudyData, featuredImage || undefined);
        toast.success('Case study updated successfully');
      } else {
        await caseStudyService.createCaseStudy(caseStudyData, featuredImage || undefined);
        toast.success('Case study created successfully');
      }

      navigate('/admin/case-studies');
    } catch (error: any) {
      console.error('Error saving case study:', error);
      toast.error(error.response?.data?.error || 'Failed to save case study');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">Loading case study...</div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? 'Edit Case Study' : 'Create Case Study'}
            </h1>
            <p className="text-gray-600 mt-1">Build a comprehensive startup success story</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" disabled={saving}>
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </Button>
            <Button onClick={() => handleSave('draft')} disabled={saving}>
              <Save className="mr-2 h-4 w-4" />
              {saving ? 'Saving...' : 'Save as Draft'}
            </Button>
            <Button onClick={() => handleSave('published')} disabled={saving}>
              <Save className="mr-2 h-4 w-4" />
              {saving ? 'Publishing...' : 'Publish'}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList>
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="sections">Content Sections</TabsTrigger>
            <TabsTrigger value="tools">Tools & CTA</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-6">
            {/* Author Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Author Selection</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="author-search-container relative">
                  <Label htmlFor="author">Select Author</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search for author..."
                      value={authorSearch}
                      onChange={(e) => setAuthorSearch(e.target.value)}
                      onFocus={() => setShowAuthorDropdown(true)}
                      className="pl-10"
                    />
                  </div>

                  {/* Show currently selected author */}
                  {selectedAuthor && authorSearch && !showAuthorDropdown && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                      <div className="text-sm text-green-800 flex items-center">
                        <User className="h-4 w-4 mr-2" />
                        <strong>Selected Author:</strong> {authorSearch} (ID: {selectedAuthor})
                      </div>
                    </div>
                  )}

                  {/* Show warning if no author selected */}
                  {!selectedAuthor && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                      <div className="text-sm text-red-800">
                        ⚠️ Please select an author
                      </div>
                    </div>
                  )}

                  {showAuthorDropdown && authors.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                      {authors.map((author) => (
                        <div
                          key={author.id || author._id}
                          className={`px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center space-x-2 ${selectedAuthor === (author.id || author._id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                            }`}
                          onMouseDown={(e) => {
                            // Prevent the input from losing focus before click is processed
                            e.preventDefault();
                          }}
                          onClick={() => {
                            const authorId = author.id || author._id;

                            if (authorId) {
                              setSelectedAuthor(authorId);
                              setAuthorSearch(author.name);
                              setShowAuthorDropdown(false);
                              toast.success(`Selected author: ${author.name}`);
                            } else {
                              console.error('Author ID is missing:', author);
                              toast.error('Invalid author selected - ID missing');
                            }
                          }}
                        >
                          {author.avatar && (
                            <img src={getAvatarUrl(author.avatar)} alt={author.name} className="w-6 h-6 rounded-full" />
                          )}
                          <div className="flex-1">
                            <div className="font-medium">{author.name}</div>
                            <div className="text-sm text-gray-500">@{author.username}</div>
                          </div>
                          {selectedAuthor === (author.id || author._id) && (
                            <div className="text-blue-600 text-sm font-medium">Selected</div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Featured Image */}
            <Card>
              <CardHeader>
                <CardTitle>Featured Image</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="hidden"
                      id="featured-image"
                    />
                    <Label htmlFor="featured-image" className="cursor-pointer">
                      <Button variant="outline" asChild>
                        <span>
                          <Upload className="mr-2 h-4 w-4" />
                          Upload Image
                        </span>
                      </Button>
                    </Label>
                  </div>
                  {featuredImagePreview && (
                    <div className="mt-4">
                      <img
                        src={featuredImagePreview}
                        alt="Featured image preview"
                        className="max-w-xs h-auto rounded-lg border"
                        onError={(e) => {
                          // Fallback to placeholder on error
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Title & Subheading */}
            <Card>
              <CardHeader>
                <CardTitle>Title & Subheading</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    placeholder="How [Startup Name] Reached $100K MRR in 12 Months"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="subheading">Subheading</Label>
                  <Input
                    id="subheading"
                    placeholder="Bootstrapped founder shares how they validated, launched, and scaled"
                    value={subheading}
                    onChange={(e) => setSubheading(e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Introduction */}
            <Card>
              <CardHeader>
                <CardTitle>Introduction (Visible to All Users)</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Brief company overview, founder's background, problem the startup solves, industry/market..."
                  value={introduction}
                  onChange={(e) => setIntroduction(e.target.value)}
                  rows={6}
                />
              </CardContent>
            </Card>

            {/* Startup Snapshot */}
            <Card>
              <CardHeader>
                <CardTitle>Startup Snapshot (Visible to All Users)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="founded">Founded</Label>
                    <Input
                      id="founded"
                      placeholder="Jan 2023"
                      value={snapshot.founded}
                      onChange={(e) => setSnapshot({ ...snapshot, founded: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="businessType">Business Type</Label>
                    <Input
                      id="businessType"
                      placeholder="SaaS, App, Marketplace"
                      value={snapshot.businessType}
                      onChange={(e) => setSnapshot({ ...snapshot, businessType: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="fundingStatus">Funding Status</Label>
                    <Input
                      id="fundingStatus"
                      placeholder="Bootstrapped or Funded"
                      value={snapshot.fundingStatus}
                      onChange={(e) => setSnapshot({ ...snapshot, fundingStatus: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="revenue">Revenue / MRR</Label>
                    <Input
                      id="revenue"
                      placeholder="$28K/month"
                      value={snapshot.revenue}
                      onChange={(e) => setSnapshot({ ...snapshot, revenue: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="customers">Customers</Label>
                    <Input
                      id="customers"
                      placeholder="3,200 users"
                      value={snapshot.customers}
                      onChange={(e) => setSnapshot({ ...snapshot, customers: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">Website URL</Label>
                    <Input
                      id="website"
                      placeholder="https://example.com"
                      value={snapshot.website}
                      onChange={(e) => setSnapshot({ ...snapshot, website: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="twitter">X Profile URL</Label>
                    <Input
                      id="twitter"
                      placeholder="https://x.com/username"
                      value={snapshot.socialLinks.twitter || ''}
                      onChange={(e) => setSnapshot({
                        ...snapshot,
                        socialLinks: { ...snapshot.socialLinks, twitter: e.target.value }
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="linkedin">LinkedIn URl</Label>
                    <Input
                      id="linkedin"
                      placeholder="https://linkedin.com/in/username"
                      value={snapshot.socialLinks.linkedin || ''}
                      onChange={(e) => setSnapshot({
                        ...snapshot,
                        socialLinks: { ...snapshot.socialLinks, linkedin: e.target.value }
                      })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sections" className="space-y-6">
            {/* Dynamic Sections */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Content Sections</CardTitle>
                <Button onClick={addSection} size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Section
                </Button>
              </CardHeader>
              <CardContent className="space-y-6">
                {sections.map((section, index) => (
                  <div key={section.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <Input
                          value={section.title}
                          onChange={(e) => updateSection(section.id!, 'title', e.target.value)}
                          className="font-medium"
                        />
                        <div className="flex items-center space-x-2">
                          {section.isPremium ? (
                            <Lock className="h-4 w-4 text-yellow-600" />
                          ) : (
                            <Unlock className="h-4 w-4 text-green-600" />
                          )}
                          <Switch
                            checked={section.isPremium}
                            onCheckedChange={(checked) => updateSection(section.id!, 'isPremium', checked)}
                          />
                          <Label>Premium</Label>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeSection(section.id!)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <Textarea
                      placeholder="Section content..."
                      value={section.content}
                      onChange={(e) => updateSection(section.id!, 'content', e.target.value)}
                      rows={4}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tools" className="space-y-6">
            {/* Tools Used */}
            <Card>
              <CardHeader>
                <CardTitle>Tools Used (Visible to All Users)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {toolsUsed.map((category, categoryIndex) => (
                  <div key={category.category}>
                    <Label className="text-base font-medium">{category.category}</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {category.tools.map((tool, toolIndex) => (
                          <Badge key={toolIndex} variant="secondary" className="flex items-center gap-1">
                            {tool}
                            <button
                              onClick={() => removeTool(categoryIndex, toolIndex)}
                              className="ml-1 hover:text-red-600"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder={`Add ${category.category.toLowerCase()} tool...`}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              addTool(categoryIndex, e.currentTarget.value);
                              e.currentTarget.value = '';
                            }
                          }}
                        />
                        <Button
                          size="sm"
                          onClick={(e) => {
                            const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                            addTool(categoryIndex, input.value);
                            input.value = '';
                          }}
                        >
                          Add
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* CTA Section */}
            <Card>
              <CardHeader>
                <CardTitle>CTA or Bonus (Visible to All Users)</CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Link to Twitter / website / newsletter, free resource download, follow me on Twitter, try my startup..."
                  value={ctaContent}
                  onChange={(e) => setCtaContent(e.target.value)}
                  rows={4}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            {/* Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Publication Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={isPremium}
                    onCheckedChange={setIsPremium}
                  />
                  <Label>Premium Case Study</Label>
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={status} onValueChange={(value: 'draft' | 'published' | 'archived') => setStatus(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Separator />
                <div>
                  <Label>Tags</Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-1 hover:text-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Input
                      placeholder="Add tag..."
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addTag()}
                    />
                    <Button size="sm" onClick={addTag}>Add</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default CaseStudyEditor;
