import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Layout from '@/components/Layout';
import CaseStudyCommentSection from '@/components/CaseStudyCommentSection';
import CaseStudySubscriptionModal from '@/components/CaseStudySubscriptionModal';
import RelatedCaseStudies from '@/components/RelatedCaseStudies';
import UserLogin from '@/components/UserLogin';
import SEO from "@/components/SEO";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Eye, Heart, Share2, Bookmark, Lock, ExternalLink, CheckCircle, ArrowLeft, Globe, Briefcase, MapPin, Home } from 'lucide-react';
import { CaseStudy, Comment, Author } from '@/types';
import caseStudyService from '@/services/caseStudyService';
import caseStudySubscriptionService from '@/services/caseStudySubscriptionService';
import favoriteService from '@/services/favoriteService';
import { useAuth } from '@/lib/AuthContext';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { useSEO } from '@/hooks/useSEO';
import { generateCaseStudyStructuredData } from '@/utils/seoUtils';
import { Helmet } from 'react-helmet-async';

// Custom X (Twitter) Icon Component
const XIcon = ({ size = 14, className = "" }: { size?: number; className?: string }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
);

// Custom LinkedIn Icon Component
const LinkedInIcon = ({ size = 14, className = "" }: { size?: number; className?: string }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className={className}>
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
  </svg>
);

// Backend URL for static assets like avatars
const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'] || 'http://localhost:5000/';

// Helper function to get complete avatar URL
const getAvatarUrl = (path: string) => {
  if (!path) return '/default-avatar.png';
  if (path.startsWith('http')) return path;
  if (path.startsWith('/')) return `${BACKEND_URL.replace(/\/$/, '')}${path}`;
  return `${BACKEND_URL.replace(/\/$/, '')}/${path}`;
};

const CaseStudyDetailPage: React.FC = () => {
  const { caseStudyId } = useParams<{ caseStudyId: string }>();
  const [caseStudy, setCaseStudy] = useState<CaseStudy | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [hasCaseStudyAccess, setHasCaseStudyAccess] = useState(false);
  const [isSubscriptionModalOpen, setIsSubscriptionModalOpen] = useState(false);
  const [showLoginDialog, setShowLoginDialog] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  const { isAuthenticated, user } = useAuth();
  const { generateSEO } = useSEO();

  useEffect(() => {
    if (caseStudyId) {
      fetchCaseStudy(caseStudyId);
    }
  }, [caseStudyId]);

  // Check case study access when user authentication status changes
  useEffect(() => {
    if (isAuthenticated) {
      checkCaseStudyAccess();
    } else {
      setHasCaseStudyAccess(false);
    }
  }, [isAuthenticated]);

  // Update like state when case study loads or authentication changes
  useEffect(() => {
    if (caseStudy && isAuthenticated && user) {
      setIsLiked(caseStudy.likedBy?.includes(user.id) || false);
    } else {
      setIsLiked(false);
    }
  }, [caseStudy?._id, isAuthenticated, user]); // Only run when case study ID changes or auth state changes

  // Check if case study is bookmarked when user is authenticated and case study is loaded
  useEffect(() => {
    const checkBookmarkStatus = async () => {
      if (isAuthenticated && caseStudy) {
        try {
          const response = await favoriteService.isFavorited('caseStudy', caseStudy._id);
          if (response.success) {
            setIsBookmarked(response.data.isFavorited);
          }
        } catch (error) {
          console.error('Error checking bookmark status:', error);
        }
      }
    };

    checkBookmarkStatus();
  }, [isAuthenticated, caseStudy]);

  const fetchCaseStudy = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const response = await caseStudyService.getCaseStudy(id);
      setCaseStudy(response.data);
      setLikesCount(response.data.likes);
    } catch (error: any) {
      console.error('Error fetching case study:', error);
      setError(error.response?.data?.error || 'Failed to load case study');
    } finally {
      setLoading(false);
    }
  };

  const checkCaseStudyAccess = async () => {
    try {
      const accessResponse = await caseStudySubscriptionService.checkAccess();
      setHasCaseStudyAccess(accessResponse.data.hasAccess);
    } catch (error) {
      console.error('Error checking case study access:', error);
      setHasCaseStudyAccess(false);
    }
  };

  const handleLike = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    if (!caseStudy || !user) return;

    try {
      const response = await caseStudyService.likeCaseStudy(caseStudy._id);
      setIsLiked(response.data.isLiked);
      setLikesCount(response.data.likes);

      // Update the case study data with new likes count and likedBy array
      setCaseStudy(prev => {
        if (!prev) return null;

        const updatedLikedBy = response.data.isLiked
          ? [...(prev.likedBy || []), user.id]
          : (prev.likedBy || []).filter(id => id !== user.id);

        return {
          ...prev,
          likes: response.data.likes,
          likedBy: updatedLikedBy
        };
      });

      toast.success(response.data.isLiked ? 'Case study liked!' : 'Like removed');
    } catch (error) {
      console.error('Error toggling like:', error);
      toast.error('Failed to update like status. Please try again.');
    }
  };

  const handleBookmark = async () => {
    if (!isAuthenticated) {
      setShowLoginDialog(true);
      return;
    }

    if (!caseStudy) return;

    try {
      const response = await favoriteService.toggleFavorite('caseStudy', caseStudy._id);

      if (response.success) {
        setIsBookmarked(response.data.isFavorited);
        toast.success(response.data.isFavorited ? 'Case study bookmarked!' : 'Bookmark removed');
      }
    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast.error('Failed to update bookmark status. Please try again.');
    }
  };

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Case study link copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy link. Please try again.');
    }
  };

  const getAuthorName = (author: string | Author) => {
    if (typeof author === 'string') return 'Unknown Author';
    return author.name || 'Unknown Author';
  };

  const getAuthorUserName = (author: string | Author) => {
    if (typeof author === 'string') return 'Unknown Author';
    return author.username || 'Unknown Author';
  };

  const getAuthorRole = (author: string | Author) => {
    if (typeof author === 'string') return '';
    return author.role || author.roleTitle || '';
  };

  const getAuthorAvatar = (author: string | Author) => {
    if (typeof author === 'string') return '';
    return author.avatar || '';
  };

  const getAuthorId = (author: string | Author) => {
    if (typeof author === 'string') return author;
    return author._id || author.id || '';
  };

  const getFeaturedImageUrl = (featuredImage?: any) => {
    if (!featuredImage) return "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=1200&h=600&fit=crop";

    const BACKEND_URL = import.meta.env['VITE_BACKEND_URL'];
    return featuredImage.url ? `${BACKEND_URL}${featuredImage.path}` : featuredImage.path || "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=1200&h=600&fit=crop";
  };

  // Generate SEO data when case study is loaded
  const caseStudySEO = caseStudy ? generateSEO.caseStudy({
    _id: caseStudy._id,
    title: caseStudy.title,
    subheading: caseStudy.subheading,
    introduction: caseStudy.introduction,
    featuredImage: caseStudy.featuredImage,
    author: {
      name: typeof caseStudy.author === 'object' ? caseStudy.author.name : 'Unknown Author',
      username: typeof caseStudy.author === 'object' ? caseStudy.author.username : 'unknown'
    },
    tags: caseStudy.tags || [],
    publishDate: caseStudy.publishDate,
    updatedAt: caseStudy.updatedAt,
    startupSnapshot: {
      businessType: caseStudy.startupSnapshot.businessType,
      fundingStatus: caseStudy.startupSnapshot.fundingStatus
    }
  }) : null;

  // Generate structured data for the case study
  const structuredData = caseStudy ? generateCaseStudyStructuredData({
    _id: caseStudy._id,
    title: caseStudy.title,
    subheading: caseStudy.subheading,
    introduction: caseStudy.introduction,
    featuredImage: caseStudy.featuredImage,
    author: {
      name: typeof caseStudy.author === 'object' ? caseStudy.author.name : 'Unknown Author',
      username: typeof caseStudy.author === 'object' ? caseStudy.author.username : 'unknown'
    },
    tags: caseStudy.tags || [],
    publishDate: caseStudy.publishDate,
    updatedAt: caseStudy.updatedAt,
    startupSnapshot: {
      businessType: caseStudy.startupSnapshot.businessType,
      fundingStatus: caseStudy.startupSnapshot.fundingStatus
    }
  }) : null;

  if (loading) {
    return (
      <Layout>
        <main className="container mx-auto py-8 flex-1">
          <div className="flex justify-center items-center py-24">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
              <div className="text-muted-foreground">Loading case study...</div>
            </div>
          </div>
        </main>
      </Layout>
    );
  }

  if (error || !caseStudy) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/30 flex items-center justify-center px-4">
          <div className="text-center max-w-lg mx-auto">
            <div className="relative">
              {/* Decorative background elements */}
              <div className="absolute inset-0 -top-20 -bottom-20">
                <div className="absolute top-8 right-12 w-2 h-2 bg-red-300/40 dark:bg-red-600/40 rounded-full animate-pulse"></div>
                <div className="absolute top-28 left-8 w-1 h-1 bg-red-400/60 dark:bg-red-500/60 rounded-full animate-pulse delay-75"></div>
                <div className="absolute bottom-16 right-20 w-1.5 h-1.5 bg-red-200/50 dark:bg-red-700/50 rounded-full animate-pulse delay-150"></div>
              </div>

              {/* Main content card */}
              <div className="relative bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-xl shadow-black/5 dark:shadow-black/20">
                {/* Large 404 text */}
                <div className="mb-6">
                  <div className="text-8xl font-bold text-transparent bg-gradient-to-br from-red-500 to-red-600 dark:from-red-400 dark:to-red-500 bg-clip-text mb-2">
                    404
                  </div>
                </div>

                {/* Icon container with enhanced styling */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/20 rounded-full flex items-center justify-center shadow-lg shadow-red-200/50 dark:shadow-red-900/20 ring-1 ring-red-200/30 dark:ring-red-700/30">
                    <Briefcase className="w-10 h-10 text-red-600 dark:text-red-400" />
                  </div>
                  {/* Subtle glow effect */}
                  <div className="absolute inset-0 w-20 h-20 mx-auto mb-4 bg-red-400/20 dark:bg-red-500/20 rounded-full blur-xl"></div>
                </div>

                <h1 className="text-2xl font-bold text-foreground mb-2 tracking-tight">
                  Case Study Not Found
                </h1>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  The case study you're looking for doesn't exist or has been removed.
                  It may have been deleted or the URL might be incorrect.
                </p>

                {/* Enhanced action buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={() => window.history.back()}
                    className="w-full bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800 text-white font-medium py-3 rounded-xl shadow-lg shadow-brand-600/25 transition-all duration-200 hover:shadow-xl hover:shadow-brand-600/30 hover:-translate-y-0.5"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Go Back
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/case-studies'}
                    className="w-full bg-background/50 hover:bg-muted/50 border-border/70 hover:border-border text-foreground font-medium py-3 rounded-xl transition-all duration-200 hover:shadow-md hover:-translate-y-0.5"
                  >
                    <Briefcase className="w-4 h-4 mr-2" />
                    Browse All Case Studies
                  </Button>
                  <Button
                    variant="ghost"
                    onClick={() => window.location.href = '/'}
                    className="w-full text-muted-foreground hover:text-foreground font-medium py-3 rounded-xl transition-all duration-200 hover:bg-muted/30"
                  >
                    <Home className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const publicSections = caseStudy.sections.filter(section => !section.isPremium);
  const premiumSections = caseStudy.sections.filter(section => section.isPremium);

  return (
    <Layout>
      {/* SEO Meta Tags */}
      {caseStudySEO && <SEO {...caseStudySEO} />}

      {/* Structured Data */}
      {structuredData && (
        <Helmet>
          <script type="application/ld+json">
            {JSON.stringify(structuredData)}
          </script>
        </Helmet>
      )}

      <main className="container mx-auto py-8 flex-1">
        {/* Hero Section */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-card rounded-xl shadow-sm border border-border overflow-hidden mb-6 sm:mb-8"
        >
          <img
            src={getFeaturedImageUrl(caseStudy.featuredImage)}
            alt={caseStudy.title}
            className="w-full h-48 sm:h-64 md:h-80 lg:h-96 object-cover"
          />
          <div className="p-4 sm:p-6 lg:p-8">
            <div className="flex flex-wrap items-center gap-2 mb-3 sm:mb-4">
              {caseStudy.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="bg-muted text-muted-foreground text-xs sm:text-sm">
                  {tag}
                </Badge>
              ))}
              {caseStudy.isPremium && (
                <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 text-xs sm:text-sm">
                  <Lock size={10} className="mr-1 sm:w-3 sm:h-3" />
                  Premium Content
                </Badge>
              )}
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-3 sm:mb-4">
              {caseStudy.title}
            </h1>
            <p className="text-lg sm:text-xl text-muted-foreground mb-4 sm:mb-6">
              {caseStudy.subheading}
            </p>

            {/* Author and Stats */}
            <div className="flex flex-col md:flex-row justify-between items-start gap-4">
              <Link to={`/author/@${getAuthorUserName(caseStudy.author)}`} className="flex items-center mb-4 md:mb-0 w-full md:w-auto">
                <img
                  src={getAvatarUrl(getAuthorAvatar(caseStudy.author))}
                  alt={getAuthorName(caseStudy.author)}
                  className="w-10 h-10 sm:w-12 sm:h-12 rounded-full mr-3 sm:mr-4 flex-shrink-0"
                />
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-sm sm:text-base text-foreground truncate">{getAuthorName(caseStudy.author)}</h3>
                    {typeof caseStudy.author === 'object' && caseStudy.author.isVerified && (
                      <div className="flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="15" height="15" viewBox="0 0 48 48" className="sm:w-5 sm:h-5 lg:w-6 lg:h-6 flex-shrink-0">
                          <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.910,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.920,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.920-0.825c0.710-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.920-0.316l-4.706-3.660c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.410-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.410l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                  <p className="text-xs sm:text-sm text-muted-foreground truncate">{getAuthorRole(caseStudy.author)}</p>
                </div>
              </Link>

              <div className="flex flex-wrap items-center gap-3 sm:gap-6 text-xs sm:text-sm text-muted-foreground w-full md:w-auto">
                <span className="flex items-center gap-1">
                  <Eye size={14} className="sm:w-4 sm:h-4" />
                  {caseStudy.views} views
                </span>
                <button
                  onClick={handleLike}
                  className={`flex items-center gap-1 hover:text-red-500 transition-colors ${isLiked ? 'text-red-500' : ''}`}
                  title={isAuthenticated ? (isLiked ? 'Unlike this case study' : 'Like this case study') : 'Login to like this case study'}
                >
                  <Heart size={14} className={`sm:w-4 sm:h-4 ${isLiked ? 'fill-current' : ''}`} />
                  {likesCount}
                </button>
                <button
                  onClick={handleBookmark}
                  className={`flex items-center gap-1 hover:text-blue-500 transition-colors ${isBookmarked ? 'text-blue-500' : ''}`}
                  title={isAuthenticated ? (isBookmarked ? 'Remove from favorites' : 'Add to favorites') : 'Login to bookmark this case study'}
                >
                  <Bookmark size={14} className={`sm:w-4 sm:h-4 ${isBookmarked ? 'fill-current' : ''}`} />
                  {isBookmarked ? 'Saved' : 'Save'}
                </button>
                <button
                  onClick={handleShare}
                  className="flex items-center gap-1 hover:text-blue-500 transition-colors"
                  title="Share this case study"
                >
                  <Share2 size={14} className="sm:w-4 sm:h-4" />
                  Share
                </button>
                <span className="hidden sm:inline">{new Date(caseStudy.publishDate).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          <div className="lg:col-span-2 space-y-6 lg:space-y-8">
            {/* Introduction */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="bg-card border-border">
                <CardHeader className="p-4 sm:p-6">
                  <CardTitle className="text-lg sm:text-xl text-foreground">Introduction</CardTitle>
                </CardHeader>
                <CardContent className="p-4 sm:p-6 pt-0">
                  <p className="text-sm sm:text-base text-muted-foreground leading-relaxed">{caseStudy.introduction}</p>
                </CardContent>
              </Card>
            </motion.div>

            {/* Public Sections */}
            {publicSections.map((section, index) => (
              <motion.div
                key={section._id || section.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 + (index * 0.1) }}
              >
                <Card className="bg-card border-border">
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-lg sm:text-xl text-foreground">{section.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 sm:p-6 pt-0">
                    <div className="prose max-w-none dark:prose-invert prose-sm sm:prose-base">
                      <p className="text-sm sm:text-base text-muted-foreground leading-relaxed whitespace-pre-line">
                        {section.content}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}

            {/* Premium Sections */}
            {premiumSections.length > 0 && (
              <div className="space-y-6 lg:space-y-8">
                {premiumSections.map((section, index) => (
                  <motion.div
                    key={section._id || section.id}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.6 + (index * 0.1) }}
                  >
                    <Card className="relative bg-card border-border">
                      <CardHeader className="p-4 sm:p-6">
                        <CardTitle className="flex items-center text-lg sm:text-xl text-foreground">
                          <Lock size={16} className="mr-2 text-yellow-600 dark:text-yellow-500 sm:w-5 sm:h-5" />
                          {section.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 sm:p-6 pt-0">
                        {hasCaseStudyAccess ? (
                          <div className="prose max-w-none dark:prose-invert prose-sm sm:prose-base">
                            <p className="text-sm sm:text-base text-muted-foreground leading-relaxed whitespace-pre-line">
                              {section.content}
                            </p>
                          </div>
                        ) : (
                          <div className="relative min-h-[300px] sm:min-h-[350px] overflow-hidden">
                            {/* Blurred preview content */}
                            <div className="blur-[2px] select-none pointer-events-none opacity-60">
                              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4 text-sm sm:text-base">
                                This premium content contains detailed insights about {section.title.toLowerCase()}. Get access to exclusive strategies, frameworks, and actionable tactics used by successful founders.
                              </p>
                              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4 text-sm sm:text-base">
                                Learn the exact methodologies, decision-making processes, and growth tactics that helped founders scale their startups from zero to millions in revenue. Discover the metrics they tracked, the mistakes they made, and the pivotal moments that changed everything.
                              </p>
                              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4 text-sm sm:text-base">
                                Get access to detailed financial breakdowns, customer acquisition strategies, product development insights, and team building approaches that you can implement in your own startup journey.
                              </p>
                              <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-sm sm:text-base">
                                This section includes step-by-step frameworks, templates, and actionable insights that have been proven to work in real-world scenarios by successful entrepreneurs.
                              </p>
                            </div>

                            {/* Premium content overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-white via-white/95 to-white/30 dark:from-gray-900 dark:via-gray-900/95 dark:to-gray-900/30 flex items-center justify-center z-10">
                              <motion.div
                                initial={{ scale: 0.9, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ delay: 0.2, duration: 0.3 }}
                                className="text-center bg-white dark:bg-gray-900 p-4 sm:p-6 lg:p-8 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 max-w-xs sm:max-w-md mx-4 relative z-20"
                              >
                                <div className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/50 dark:to-blue-800/50 rounded-full w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg">
                                  <Lock size={24} className="text-blue-600 dark:text-blue-400 sm:w-8 sm:h-8" />
                                </div>
                                <h3 className="font-bold text-lg sm:text-xl mb-2 sm:mb-3 text-gray-900 dark:text-white">Unlock Premium Case Study Content</h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4 sm:mb-6 text-xs sm:text-sm leading-relaxed">
                                  Unlock all premium sections across every case study. Get the strategies, metrics, and insights that helped founders build successful companies.
                                </p>
                                <Button
                                  onClick={() => {
                                    if (!isAuthenticated) {
                                      setShowLoginDialog(true);
                                    } else {
                                      setIsSubscriptionModalOpen(true);
                                    }
                                  }}
                                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white w-full font-semibold shadow-lg hover:shadow-xl transition-all duration-200 text-sm sm:text-base"
                                  size={window.innerWidth < 640 ? "sm" : "lg"}
                                >
                                  {isAuthenticated ? 'Subscribe to Read' : 'Login to Subscribe'}
                                </Button>
                              </motion.div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}

            {/* CTA Section */}
            {caseStudy.ctaContent && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-lg sm:text-xl text-blue-900 dark:text-blue-100">Follow the Journey</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 sm:p-6 pt-0">
                    <p className="text-blue-800 dark:text-blue-200 mb-4 text-sm sm:text-base">{caseStudy.ctaContent}</p>
                    <div className="flex flex-wrap gap-2">
                      {typeof caseStudy.author === 'object' && caseStudy.author.twitterUrl && (
                        <Button variant="outline" size="sm" asChild className="border-border hover:bg-muted text-xs sm:text-sm">
                          <a href={caseStudy.author.twitterUrl} target="_blank" rel="noopener noreferrer">
                            <ExternalLink size={12} className="mr-1 sm:w-[14px] sm:h-[14px]" />
                            Twitter
                          </a>
                        </Button>
                      )}
                      {typeof caseStudy.author === 'object' && caseStudy.author.websiteUrl && (
                        <Button variant="outline" size="sm" asChild className="border-border hover:bg-muted text-xs sm:text-sm">
                          <a href={caseStudy.author.websiteUrl} target="_blank" rel="noopener noreferrer">
                            <ExternalLink size={12} className="mr-1 sm:w-[14px] sm:h-[14px]" />
                            Website
                          </a>
                        </Button>
                      )}
                      <Button variant="outline" size="sm" asChild className="border-border hover:bg-muted text-xs sm:text-sm">
                        <a href={caseStudy.startupSnapshot.website} target="_blank" rel="noopener noreferrer">
                          <ExternalLink size={12} className="mr-1 sm:w-[14px] sm:h-[14px]" />
                          Try {caseStudy.title.split(' ')[1] || 'Product'}
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Author Bio */}
            {typeof caseStudy.author === 'object' && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.95 }}
                className="mt-4 sm:mt-6 lg:mt-8 bg-card rounded-xl shadow-sm p-3 sm:p-4 lg:p-6 xl:p-8 border border-border"
              >
                <div className="flex flex-col items-center space-y-3 sm:space-y-0 sm:flex-row sm:items-start sm:space-x-4">
                  {/* Avatar - centered on mobile, left-aligned on larger screens */}
                  <img
                    src={getAvatarUrl(getAuthorAvatar(caseStudy.author))}
                    alt={getAuthorName(caseStudy.author)}
                    className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 rounded-full ring-2 sm:ring-4 ring-brand-50 flex-shrink-0"
                  />

                  {/* Author info - stacked on mobile, side-by-side on larger screens */}
                  <div className="flex-1 text-center sm:text-left space-y-2 sm:space-y-3">
                    {/* Name and verification badge */}
                    <div className="flex flex-row items-center justify-center sm:justify-start gap-2">
                      <Link
                        to={`/author/@${getAuthorUserName(caseStudy.author)}`}
                        className="font-bold text-base sm:text-lg lg:text-xl hover:text-brand-600 transition-colors text-foreground"
                      >
                        {getAuthorName(caseStudy.author)}
                      </Link>
                      {typeof caseStudy.author !== 'string' && caseStudy.author.isVerified && (
                        <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="16" height="16" viewBox="0 0 48 48" className="sm:w-5 sm:h-5 lg:w-6 lg:h-6 flex-shrink-0">
                          <linearGradient id="csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1" x1="24" x2="24" y1="3.999" y2="43.001" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#2aa4f4"></stop><stop offset="1" stopColor="#007ad9"></stop></linearGradient><path fill="url(#csF85US9HGjIK87qotE6pa_QMxOVe0B9VzG_gr1)" d="M43.466,25.705l-2.599-4.259l1.293-4.817c0.187-0.694-0.146-1.424-0.793-1.738l-4.488-2.178	l-1.518-4.752c-0.219-0.686-0.888-1.114-1.607-1.033l-4.953,0.594l-3.846-3.178c-0.555-0.459-1.355-0.459-1.910,0l-3.846,3.178	l-4.953-0.594c-0.717-0.081-1.389,0.348-1.607,1.033l-1.518,4.752l-4.488,2.178c-0.646,0.314-0.979,1.044-0.793,1.738l1.293,4.817	l-2.599,4.259c-0.375,0.614-0.261,1.408,0.271,1.892l3.693,3.354l0.116,4.987c0.018,0.719,0.542,1.325,1.252,1.444l4.920,0.825	l2.795,4.133c0.403,0.595,1.172,0.822,1.833,0.538L24,40.913l4.585,1.966C28.776,42.961,28.977,43,29.175,43	c0.486,0,0.957-0.236,1.243-0.659l2.795-4.133l4.920-0.825c0.710-0.119,1.234-0.726,1.252-1.444l0.116-4.987l3.693-3.354	C43.727,27.113,43.841,26.319,43.466,25.705z"></path><path fill="#fff" d="M21.814,31c-0.322,0-0.646-0.104-0.920-0.316l-4.706-3.660c-0.436-0.339-0.514-0.967-0.175-1.403	l0.614-0.789c0.339-0.436,0.967-0.514,1.403-0.175l3.581,2.785l7.086-8.209c0.361-0.418,0.992-0.464,1.410-0.104l0.757,0.653	c0.418,0.361,0.464,0.992,0.104,1.410l-8.017,9.289C22.655,30.822,22.236,31,21.814,31z"></path>
                        </svg>
                      )}
                    </div>

                    {/* Location and role - stacked on mobile */}
                    <div className="flex flex-col sm:flex-row sm:items-center justify-center sm:justify-start gap-1 sm:gap-3 text-xs sm:text-sm text-muted-foreground">
                      {typeof caseStudy.author === 'object' && caseStudy.author.location && (
                        <div className="flex items-center justify-center sm:justify-start gap-1">
                          <MapPin size={12} className="text-muted-foreground/70 flex-shrink-0" />
                          <span className="truncate">{caseStudy.author.location}</span>
                        </div>
                      )}
                      <div className="flex items-center justify-center sm:justify-start gap-1">
                        <Briefcase size={12} className="text-muted-foreground/70 flex-shrink-0" />
                        <span className="truncate">{getAuthorRole(caseStudy.author) || 'Business Analyst'}</span>
                      </div>
                    </div>

                    {/* Social links - centered on mobile, left-aligned on larger screens */}
                    <div className="flex items-center justify-center sm:justify-start gap-2 sm:gap-3 pt-1">
                      {typeof caseStudy.author === 'object' && caseStudy.author.websiteUrl && (
                        <a
                          href={caseStudy.author.websiteUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                          title="Website"
                        >
                          <Globe size={14} className="sm:w-4 sm:h-4" />
                        </a>
                      )}
                      {typeof caseStudy.author === 'object' && caseStudy.author.twitterUrl && (
                        <a
                          href={caseStudy.author.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                          title="Twitter"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="sm:w-4 sm:h-4">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                          </svg>
                        </a>
                      )}
                      {typeof caseStudy.author === 'object' && caseStudy.author.linkedinUrl && (
                        <a
                          href={caseStudy.author.linkedinUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-1 text-muted-foreground hover:text-brand-600 transition-colors rounded-full hover:bg-muted/50"
                          title="LinkedIn"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" className="sm:w-4 sm:h-4">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                          </svg>
                        </a>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bio text - separate section for better mobile layout */}
                {typeof caseStudy.author === 'object' && caseStudy.author.bio && (
                  <div className="mt-3 sm:mt-4 lg:mt-6 pt-3 sm:pt-4 border-t border-border/50">
                    <p className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-relaxed text-center sm:text-left">
                      {caseStudy.author.bio}
                    </p>
                  </div>
                )}
              </motion.div>
            )}

            {/* Comment Section */}
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9 }}
              className="mt-8 sm:mt-12"
            >
              <CaseStudyCommentSection caseStudyId={caseStudy._id} />
            </motion.div>

            {/* Related Case Studies */}
            {typeof caseStudy.author === 'object' && (
              <RelatedCaseStudies
                authorId={caseStudy.author._id || caseStudy.author.id}
                authorName={caseStudy.author.name}
                authorUsername={caseStudy.author.username}
                currentCaseStudyId={caseStudy._id}
                limit={3}
              />
            )}
          </div>

          {/* Sidebar */}
          <motion.div
            initial={{ x: 20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="space-y-4 sm:space-y-6 lg:sticky lg:top-8 lg:h-fit order-first lg:order-last"
          >
            {/* Startup Snapshot */}
            <Card className="bg-card border-border">
              <CardHeader className="p-3 sm:p-4 lg:p-6">
                <CardTitle className="text-base sm:text-lg lg:text-xl text-foreground">Startup Snapshot</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 sm:space-y-3 max-h-64 sm:max-h-80 lg:max-h-96 overflow-y-auto p-3 sm:p-4 lg:p-6 pt-0">
                <div className="flex justify-between items-start gap-2">
                  <span className="text-muted-foreground text-xs sm:text-sm lg:text-base flex-shrink-0">Founded</span>
                  <span className="font-medium text-foreground text-right text-xs sm:text-sm lg:text-base break-words">{caseStudy.startupSnapshot.founded}</span>
                </div>
                <Separator className="bg-border" />
                <div className="flex justify-between items-start gap-2">
                  <span className="text-muted-foreground text-xs sm:text-sm lg:text-base flex-shrink-0">Business Type</span>
                  <span className="font-medium text-foreground text-right text-xs sm:text-sm lg:text-base break-words">{caseStudy.startupSnapshot.businessType}</span>
                </div>
                <Separator className="bg-border" />
                <div className="flex justify-between items-start gap-2">
                  <span className="text-muted-foreground text-xs sm:text-sm lg:text-base flex-shrink-0">Funding Status</span>
                  <span className="font-medium text-foreground text-right text-xs sm:text-sm lg:text-base break-words">{caseStudy.startupSnapshot.fundingStatus}</span>
                </div>
                <Separator className="bg-border" />
                <div className="flex justify-between items-start gap-2">
                  <span className="text-muted-foreground text-xs sm:text-sm lg:text-base flex-shrink-0">Revenue</span>
                  <span className="font-medium text-green-600 dark:text-green-500 text-right text-xs sm:text-sm lg:text-base break-words">{caseStudy.startupSnapshot.revenue}</span>
                </div>
                <Separator className="bg-border" />
                <div className="flex justify-between items-start gap-2">
                  <span className="text-muted-foreground text-xs sm:text-sm lg:text-base flex-shrink-0">Customers</span>
                  <span className="font-medium text-foreground text-right text-xs sm:text-sm lg:text-base break-words">{caseStudy.startupSnapshot.customers}</span>
                </div>
                <Separator className="bg-border" />
                 
                {/* Social Links */}
                {(caseStudy.startupSnapshot.socialLinks?.twitter || caseStudy.startupSnapshot.socialLinks?.linkedin) && (
                  <>
                    <div className="flex justify-between items-center gap-2">
                      <span className="text-muted-foreground text-xs sm:text-sm lg:text-base flex-shrink-0">Social Links</span>
                      <div className="flex gap-2">
                      <Button variant="outline" size="sm" asChild className="border-border hover:bg-muted flex-shrink-0">
                    <a href={caseStudy.startupSnapshot.website} target="_blank" rel="noopener noreferrer">
                      <ExternalLink size={10} className="sm:w-3 sm:h-3 lg:w-[14px] lg:h-[14px]" />
                    </a>
                  </Button>
                        {caseStudy.startupSnapshot.socialLinks?.twitter && (
                          <Button variant="outline" size="sm" asChild className="border-border hover:bg-muted flex-shrink-0">
                            <a
                              href={caseStudy.startupSnapshot.socialLinks.twitter}
                              target="_blank"
                              rel="noopener noreferrer"
                              title="Twitter"
                            >
                              <XIcon size={10} className="sm:w-3 sm:h-3 lg:w-[14px] lg:h-[14px]" />
                            </a>
                          </Button>
                        )}
                        {caseStudy.startupSnapshot.socialLinks?.linkedin && (
                          <Button variant="outline" size="sm" asChild className="border-border hover:bg-muted flex-shrink-0">
                            <a
                              href={caseStudy.startupSnapshot.socialLinks.linkedin}
                              target="_blank"
                              rel="noopener noreferrer"
                              title="LinkedIn"
                            >
                              <LinkedInIcon size={10} className="sm:w-3 sm:h-3 lg:w-[14px] lg:h-[14px]" />
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  </>
                )}

              </CardContent>
            </Card>

            {/* Tools Used */}
            <Card className="bg-card border-border">
              <CardHeader className="p-3 sm:p-4 lg:p-6">
                <CardTitle className="text-base sm:text-lg lg:text-xl text-foreground">Tools Used</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4 max-h-64 sm:max-h-80 lg:max-h-96 overflow-y-auto p-3 sm:p-4 lg:p-6 pt-0">
                {caseStudy.toolsUsed.map((category) => (
                  <div key={category.category} className="space-y-2">
                    <h4 className="font-medium text-foreground text-xs sm:text-sm lg:text-base">{category.category}</h4>
                    <div className="flex flex-wrap gap-1">
                      {category.tools.map((tool) => (
                        <Badge key={tool} variant="outline" className="text-[10px] sm:text-xs border-border text-muted-foreground hover:bg-muted transition-colors px-1.5 py-0.5 sm:px-2 sm:py-1">
                          {tool}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </main>

      {/* Login Dialog */}
      <UserLogin
        isOpen={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
      />

      {/* Scroll-triggered Login Popup */}
      {/* <UserLogin
        isOpen={scrollLoginPopup.isPopupOpen}
        onClose={scrollLoginPopup.closePopup}
        preventScroll={true}
        blurBackground={true}
        compulsory={scrollLoginPopup.isCompulsory}
      /> */}

      {/* Case Study Subscription Modal */}
      <CaseStudySubscriptionModal
        isOpen={isSubscriptionModalOpen}
        onClose={() => setIsSubscriptionModalOpen(false)}
      />
    </Layout>
  );
};

export default CaseStudyDetailPage;